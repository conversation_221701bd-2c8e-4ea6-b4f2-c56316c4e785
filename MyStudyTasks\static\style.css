/* تنسيقات عامة */
body {
  font-family: "<PERSON><PERSON><PERSON>", sans-serif;
  background-color: #ffffff;
}

/* تنسيق البطاقات */
.card {
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

/* تنسيق أزرار الحالة */
.status-buttons .btn {
  font-size: 0.8rem;
  padding: 0.25rem 0.5rem;
}

.status-buttons .btn.active {
  font-weight: bold;
}

/* تنسيق رأس البطاقة */
.card-header {
  font-weight: bold;
}

/* تنسيق شريط التنقل */
.navbar {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* تنسيق النماذج */
.form-control:focus {
  border-color: #212529;
  box-shadow: 0 0 0 0.25rem rgba(33, 37, 41, 0.25);
}

.form-check-input:checked {
  background-color: #212529;
  border-color: #212529;
}
/* تنسيق الأزرار */
.btn {
  border-radius: 4px;
  padding: 0.375rem 1rem;
}

/* تنسيق التنبيهات */
.alert {
  border-radius: 4px;
}

/* تنسيق للشاشات الصغيرة */
@media (max-width: 768px) {
  .task-card {
    margin-bottom: 1rem;
  }
}

/* تنسيق شريط التنقل المخصص */
.custom-navbar {
  background-color: #ffffff !important;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.custom-navbar .navbar-brand {
  color: #212529 !important;
}

/* تنسيق عناصر القائمة للمستخدمين المسجلين */
.custom-navbar .navbar-nav .nav-link {
  color: #212529 !important;
  font-weight: 500;
  transition: all 0.3s ease;
}

.custom-navbar .navbar-nav .nav-link:hover {
  color: #407bff !important;
  transform: translateY(-2px);
}

.custom-navbar .navbar-nav .nav-link.active {
  color: #407bff !important;
  font-weight: 700;
  border-bottom: 2px solid #407bff;
}

/* تنسيق نص الترحيب */
.custom-navbar .navbar-text {
  color: #212529 !important;
  font-weight: 500;
}

/* تنسيق زر تسجيل الخروج */
.custom-navbar .btn-outline-primary {
  border-color: #007bff;
  color: #007bff;
  font-weight: 500;
  transition: all 0.3s ease;
}

.custom-navbar .btn-outline-primary:hover {
  background-color: #007bff;
  border-color: #007bff;
  color: #ffffff;
  transform: translateY(-2px);
}

/* تنسيق روابط التنقل للمستخدمين غير المسجلين */
.navbar .d-flex .nav-link {
  color: #212529 !important;
  transition: color 0.3s ease;
  font-weight: 500;
  margin: 0 5px;
}

.navbar .d-flex .nav-link:hover {
  color: #000000;
}

html {
  scroll-behavior: smooth;
}

/* تنسيق لضمان بقاء التذييل في أسفل الصفحة */
html {
  height: 100%;
}

body {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

main {
  flex: 1 0 auto;
}

.footer {
  flex-shrink: 0;
  margin-top: auto;
  background-color: #f8f9fa;
  border-top: 1px solid #dee2e6;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

/* تحسين الـ footer الأفقي */
.footer .footer-brand {
  flex: 1;
}

.footer .footer-brand span:first-child {
  font-size: 1.1rem;
  color: #212529;
  font-weight: 600;
}

.footer .footer-brand .text-muted {
  font-size: 0.9rem;
}

/* تحسين الفاصل */
.footer .footer-brand .mx-2 {
  color: #dee2e6 !important;
  font-weight: 300;
}

/* تأثير hover للعلامة التجارية */
.footer .footer-brand:hover span:first-child {
  color: #007bff;
  transition: color 0.3s ease;
  cursor: pointer;
}

.footer .footer-links {
  flex: 2;
  text-align: center;
}

.footer .footer-links a {
  transition: color 0.3s ease;
  text-decoration: none;
  font-weight: 500;
  font-size: 0.9rem;
}

.footer .footer-links a:hover {
  color: #007bff !important;
  transform: translateY(-1px);
}

.footer .footer-social {
  flex: 1;
  text-align: left;
}

/* تحسين الأيقونات الاجتماعية */
.footer .social-icons a {
  display: inline-block;
  width: 30px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  border-radius: 50%;
  background-color: #e9ecef;
  color: #6c757d;
  transition: all 0.3s ease;
  font-size: 14px;
}

.footer .social-icons a:hover {
  background-color: #007bff;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

/* تحسين spacing للأيقونات */
.footer .social-icons a:not(:last-child) {
  margin-left: 8px;
}

/* تحسين حقوق النشر */
.footer .footer-social span {
  font-size: 0.85rem;
  font-weight: 500;
  color: #6c757d;
}

/* تحسين responsive للـ footer */
@media (max-width: 768px) {
  .footer .footer-brand,
  .footer .footer-links,
  .footer .footer-social {
    flex: none;
    text-align: center !important;
  }

  .footer .footer-links {
    margin: 15px 0;
  }

  .footer .footer-links a {
    display: inline-block;
    margin: 5px 15px;
    font-size: 0.9rem;
  }

  .footer .social-icons {
    margin-bottom: 10px;
    justify-content: center;
  }

  .footer .footer-brand span:first-child {
    font-size: 1rem;
  }
}

/* تحسين للشاشات الكبيرة */
@media (min-width: 1200px) {
  .footer .container {
    max-width: 1140px;
  }

  .footer .footer-links a {
    margin: 0 20px;
    font-size: 0.95rem;
  }

  .footer .footer-brand span:first-child {
    font-size: 1.2rem;
  }
}

/* تحسينات إضافية للتفاعل */
.footer .footer-brand i {
  color: #28a745;
  transition: transform 0.3s ease;
}

.footer .footer-brand:hover i {
  transform: rotate(360deg);
}

/* تحسين الانتقالات */
.footer * {
  transition: all 0.3s ease;
}

/* تحسين الألوان للوضع المظلم (اختياري) */
@media (prefers-color-scheme: dark) {
  .footer {
    background-color: #2d3748;
    border-top-color: #4a5568;
  }

  .footer .footer-brand span:first-child {
    color: #e2e8f0;
  }

  .footer .text-muted {
    color: #a0aec0 !important;
  }

  .footer .social-icons a {
    background-color: #4a5568;
    color: #a0aec0;
  }
}
