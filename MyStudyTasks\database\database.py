import sqlite3
import hashlib
from datetime import datetime
import os

class Database:
    def __init__(self, db_path='database/study_tasks.db'):
        """تهيئة قاعدة البيانات"""
        self.db_path = db_path
        self.init_database()
    
    def get_connection(self):
        """الحصول على اتصال بقاعدة البيانات"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # للحصول على النتائج كقاموس
        return conn
    
    def init_database(self):
        """إنشاء الجداول إذا لم تكن موجودة"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # إنشاء جدول المستخدمين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # إنشاء جدول المهام
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS tasks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                description TEXT,
                due_date DATE,
                status TEXT DEFAULT 'جاري',
                user_id INTEGER NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        conn.commit()
        conn.close()
        print("تم إنشاء قاعدة البيانات والجداول بنجاح!")
    
    def hash_password(self, password):
        """تشفير كلمة المرور"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def verify_password(self, password, password_hash):
        """التحقق من كلمة المرور"""
        return self.hash_password(password) == password_hash
    
    # ===== عمليات المستخدمين =====
    
    def create_user(self, username, password):
        """إنشاء مستخدم جديد"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            password_hash = self.hash_password(password)
            cursor.execute(
                'INSERT INTO users (username, password_hash) VALUES (?, ?)',
                (username, password_hash)
            )
            conn.commit()
            user_id = cursor.lastrowid
            conn.close()
            return user_id
        except sqlite3.IntegrityError:
            conn.close()
            return None  # المستخدم موجود بالفعل
    
    def get_user_by_username(self, username):
        """الحصول على مستخدم بواسطة اسم المستخدم"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM users WHERE username = ?', (username,))
        user = cursor.fetchone()
        conn.close()
        
        return dict(user) if user else None
    
    def get_user_by_id(self, user_id):
        """الحصول على مستخدم بواسطة المعرف"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM users WHERE id = ?', (user_id,))
        user = cursor.fetchone()
        conn.close()
        
        return dict(user) if user else None
    
    def authenticate_user(self, username, password):
        """التحقق من صحة بيانات المستخدم"""
        user = self.get_user_by_username(username)
        if user and self.verify_password(password, user['password_hash']):
            return user
        return None
    
    def get_all_users(self):
        """الحصول على جميع المستخدمين"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('SELECT id, username, created_at FROM users ORDER BY username')
        users = cursor.fetchall()
        conn.close()
        
        return [dict(user) for user in users]
    
    # ===== عمليات المهام =====
    
    def create_task(self, title, description, due_date, status, user_id):
        """إنشاء مهمة جديدة"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO tasks (title, description, due_date, status, user_id)
            VALUES (?, ?, ?, ?, ?)
        ''', (title, description, due_date, status, user_id))
        
        conn.commit()
        task_id = cursor.lastrowid
        conn.close()
        
        return task_id
    
    def get_tasks_by_user(self, user_id):
        """الحصول على مهام مستخدم معين"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT * FROM tasks 
            WHERE user_id = ? 
            ORDER BY due_date ASC
        ''', (user_id,))
        
        tasks = cursor.fetchall()
        conn.close()
        
        return [dict(task) for task in tasks]
    
    def get_task_by_id(self, task_id):
        """الحصول على مهمة بواسطة المعرف"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM tasks WHERE id = ?', (task_id,))
        task = cursor.fetchone()
        conn.close()
        
        return dict(task) if task else None
    
    def update_task(self, task_id, title, description, due_date, status):
        """تحديث مهمة"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            UPDATE tasks 
            SET title = ?, description = ?, due_date = ?, status = ?, 
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        ''', (title, description, due_date, status, task_id))
        
        conn.commit()
        rows_affected = cursor.rowcount
        conn.close()
        
        return rows_affected > 0
    
    def update_task_status(self, task_id, status):
        """تحديث حالة المهمة فقط"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            UPDATE tasks 
            SET status = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        ''', (status, task_id))
        
        conn.commit()
        rows_affected = cursor.rowcount
        conn.close()
        
        return rows_affected > 0
    
    def delete_task(self, task_id):
        """حذف مهمة"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('DELETE FROM tasks WHERE id = ?', (task_id,))
        
        conn.commit()
        rows_affected = cursor.rowcount
        conn.close()
        
        return rows_affected > 0
    
    def get_task_statistics(self, user_id):
        """الحصول على إحصائيات المهام للمستخدم"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT 
                status,
                COUNT(*) as count
            FROM tasks 
            WHERE user_id = ?
            GROUP BY status
        ''', (user_id,))
        
        stats = cursor.fetchall()
        conn.close()
        
        return {stat['status']: stat['count'] for stat in stats}
    
    def close_database(self):
        """إغلاق قاعدة البيانات (للتنظيف)"""
        # في SQLite، الاتصالات تُغلق تلقائياً
        pass

# مثال على الاستخدام
if __name__ == '__main__':
    # إنشاء قاعدة البيانات
    db = Database()
    
    # إنشاء مستخدم تجريبي
    user_id = db.create_user('admin', 'admin123')
    if user_id:
        print(f"تم إنشاء المستخدم بمعرف: {user_id}")
        
        # إنشاء مهمة تجريبية
        task_id = db.create_task(
            title='مهمة تجريبية',
            description='هذه مهمة للاختبار',
            due_date='2024-12-31',
            status='جاري',
            user_id=user_id
        )
        print(f"تم إنشاء المهمة بمعرف: {task_id}")
        
        # عرض المهام
        tasks = db.get_tasks_by_user(user_id)
        print("المهام:")
        for task in tasks:
            print(f"- {task['title']}: {task['status']}")
    else:
        print("المستخدم موجود بالفعل!")
