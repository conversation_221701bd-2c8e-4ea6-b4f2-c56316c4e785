from flask import Flask, render_template, redirect, url_for, request, flash, jsonify, session
from database import Database
from datetime import datetime
import os

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here-change-in-production'
app.config['PERMANENT_SESSION_LIFETIME'] = 3600  # ساعة واحدة

# إنشاء قاعدة البيانات
db = Database('study_tasks.db')

# التحقق من تسجيل الدخول
def login_required(view):
    def wrapped_view(*args, **kwargs):
        if 'user_id' not in session:
            return redirect(url_for('login'))
        return view(*args, **kwargs)
    wrapped_view.__name__ = view.__name__
    return wrapped_view

# صفحة الرئيسية
@app.route('/')
def index():
    if 'user_id' in session:
        return redirect(url_for('dashboard'))
    return render_template('index.html')    

# صفحة تسجيل الدخول
@app.route('/login', methods=['GET', 'POST'])
def login():
    if 'user_id' in session:
        return redirect(url_for('dashboard'))

    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']

        user = db.authenticate_user(username, password)

        if user:
            session.clear()
            session['user_id'] = user['id']
            session['username'] = user['username']
            session.permanent = True
            app.logger.info(f'{username} تم تسجيل الدخول بنجاح')
            return redirect(url_for('dashboard'))

        flash('اسم المستخدم أو كلمة المرور غير صحيحة')

    return render_template('login.html')

# تسجيل الخروج
@app.route('/logout')
def logout():
    session.clear()
    flash('تم تسجيل الخروج بنجاح')
    return redirect(url_for('login'))

# لوحة المهام
@app.route('/dashboard')
@login_required
def dashboard():
    user_id = session['user_id']
    tasks = db.get_tasks_by_user(user_id)
    return render_template('dashboard.html', tasks=tasks)

# إضافة مهمة جديدة
@app.route('/task/add', methods=['GET', 'POST'])
@login_required
def add_task():
    if request.method == 'POST':
        title = request.form['title']
        description = request.form['description']
        due_date = request.form['due_date']  # تبقى كنص
        status = request.form['status']

        task_id = db.create_task(
            title=title,
            description=description,
            due_date=due_date,
            status=status,
            user_id=session['user_id']
        )

        if task_id:
            flash('تمت إضافة المهمة بنجاح')
        else:
            flash('حدث خطأ في إضافة المهمة')

        return redirect(url_for('dashboard'))

    return render_template('task_form.html')

# تعديل مهمة
@app.route('/task/edit/<int:task_id>', methods=['GET', 'POST'])
@login_required
def edit_task(task_id):
    task = db.get_task_by_id(task_id)

    if not task:
        flash('المهمة غير موجودة')
        return redirect(url_for('dashboard'))

    # التحقق من ملكية المهمة
    if task['user_id'] != session['user_id']:
        flash('ليس لديك صلاحية تعديل هذه المهمة')
        return redirect(url_for('dashboard'))

    if request.method == 'POST':
        title = request.form['title']
        description = request.form['description']
        due_date = request.form['due_date']
        status = request.form['status']

        success = db.update_task(task_id, title, description, due_date, status)

        if success:
            flash('تم تحديث المهمة بنجاح')
        else:
            flash('حدث خطأ في تحديث المهمة')

        return redirect(url_for('dashboard'))

    return render_template('task_form.html', task=task)

# حذف مهمة
@app.route('/task/delete/<int:task_id>', methods=['POST'])
@login_required
def delete_task(task_id):
    task = db.get_task_by_id(task_id)

    if not task:
        return jsonify({'success': False, 'message': 'المهمة غير موجودة'})

    # التحقق من ملكية المهمة
    if task['user_id'] != session['user_id']:
        return jsonify({'success': False, 'message': 'ليس لديك صلاحية حذف هذه المهمة'})

    success = db.delete_task(task_id)

    if success:
        return jsonify({'success': True})
    else:
        return jsonify({'success': False, 'message': 'حدث خطأ في حذف المهمة'})

# تحديث حالة المهمة
@app.route('/task/update_status/<int:task_id>', methods=['POST'])
@login_required
def update_status(task_id):
    task = db.get_task_by_id(task_id)

    if not task:
        return jsonify({'success': False, 'message': 'المهمة غير موجودة'})

    # التحقق من ملكية المهمة
    if task['user_id'] != session['user_id']:
        return jsonify({'success': False, 'message': 'ليس لديك صلاحية تعديل هذه المهمة'})

    status = request.form.get('status')
    if status in ['جاري', 'مكتمل', 'متأخر']:
        success = db.update_task_status(task_id, status)
        if success:
            return jsonify({'success': True})
        else:
            return jsonify({'success': False, 'message': 'حدث خطأ في تحديث الحالة'})

    return jsonify({'success': False, 'message': 'حالة غير صالحة'})

# إنشاء مستخدم (للاختبار فقط)
@app.route('/setup')
def setup():
    # التحقق من وجود مستخدمين
    all_users = db.get_all_users()
    if len(all_users) == 0:
        user_id = db.create_user('admin', 'admin123')
        if user_id:
            return 'تم إنشاء المستخدم بنجاح!'
        else:
            return 'حدث خطأ في إنشاء المستخدم!'
    return 'المستخدم موجود بالفعل!'

# صفحة التسجيل
@app.route('/register', methods=['GET', 'POST'])
def register():
    if 'user_id' in session:
        return redirect(url_for('dashboard'))

    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        confirm_password = request.form['confirm_password']

        # التحقق من تطابق كلمات المرور
        if password != confirm_password:
            flash('كلمات المرور غير متطابقة')
            return render_template('register.html')

        # التحقق من عدم وجود المستخدم مسبق
        existing_user = db.get_user_by_username(username)
        if existing_user:
            flash('اسم المستخدم موجود بالفعل')
            return render_template('register.html')

        # إنشاء مستخدم جديد
        user_id = db.create_user(username, password)

        if user_id:
            flash('تم إنشاء الحساب بنجاح، يمكنك الآن تسجيل الدخول')
            return redirect(url_for('login'))
        else:
            flash('حدث خطأ في إنشاء الحساب')

    return render_template('register.html')


# عرض قائمة المستخدمين (لجميع المستخدمين المسجلين)
@app.route('/users')
@login_required
def users():
    users_list = db.get_all_users()

    # إضافة عدد المهام لكل مستخدم
    for user in users_list:
        user_tasks = db.get_tasks_by_user(user['id'])
        user['tasks_count'] = len(user_tasks)

    return render_template('users.html', users=users_list)

if __name__ == '__main__':
    app.run(debug=True)
